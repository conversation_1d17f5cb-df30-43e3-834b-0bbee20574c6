import { JC_Get } from "../apiServices/JC_Get";
import { JC_GetList } from "../apiServices/JC_GetList";
import { JC_Post } from "../apiServices/JC_Post";
import { JC_Put } from "../apiServices/JC_Put";
import { JC_Delete } from "../apiServices/JC_Delete";
import { _Base } from "./_Base";

export class O_OtherTimberBldgElementsModel extends _Base {

    // - -------- - //
    // - SERVICES - //
    // - -------- - //

    static apiRoute:string = "o_otherTimberBldgElements";
    static async Get(code: string) {
        return JC_Get<O_OtherTimberBldgElementsModel>(this.apiRoute, { code }, O_OtherTimberBldgElementsModel);
    }
    static async GetList() {
        return JC_GetList<O_OtherTimberBldgElementsModel>(`${this.apiRoute}/getList`, {}, O_OtherTimberBldgElementsModel);
    }
    static async Create(data: O_OtherTimberBldgElementsModel) {
        return JC_Put<O_OtherTimberBldgElementsModel>(this.apiRoute, data);
    }
    static async CreateList(dataList: O_OtherTimberBldgElementsModel[]) {
        return JC_Put<O_OtherTimberBldgElementsModel[]>(`${this.apiRoute}/createList`, dataList);
    }
    static async Update(data: O_OtherTimberBldgElementsModel) {
        return JC_Post<O_OtherTimberBldgElementsModel>(this.apiRoute, data);
    }
    static async UpdateList(dataList: O_OtherTimberBldgElementsModel[]) {
        return JC_Post<O_OtherTimberBldgElementsModel[]>(`${this.apiRoute}/updateList`, dataList);
    }
    static async Delete(code: string) {
        return JC_Delete(this.apiRoute, code);
    }
    static async DeleteList(codes: string[]) {
        return JC_Post(`${this.apiRoute}/deleteList`, { codes });
    }

    // - --------- - //
    // - VARIABLES - //
    // - --------- - //

    Code: string;
    Name: string;
    SortOrder: number;

    // - ----------- - //
    // - CONSTRUCTOR - //
    // - ----------- - //

    constructor(init?: Partial<O_OtherTimberBldgElementsModel>) {
        super(init);
        this.Code = "";
        this.Name = "";
        this.SortOrder = 999;
        Object.assign(this, init);
    }

    // - ------ - //
    // - STRING - //
    // - ------ - //

    toString() {
        return `${this.Code} | ${this.Name}`;
    }
}
