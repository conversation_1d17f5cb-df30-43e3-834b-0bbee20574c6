import { JC_Get } from "../apiServices/JC_Get";
import { JC_GetList } from "../apiServices/JC_GetList";
import { JC_Post } from "../apiServices/JC_Post";
import { JC_Put } from "../apiServices/JC_Put";
import { JC_Delete } from "../apiServices/JC_Delete";
import { JC_Utils } from "../Utils";
import { _Base } from "./_Base";

export class PropertyDefectModel extends _Base {

    // - -------- - //
    // - SERVICES - //
    // - -------- - //

    static apiRoute:string = "propertyDefect";
    static async Get(id: string) {
        return JC_Get<PropertyDefectModel>(this.apiRoute, { id }, PropertyDefectModel);
    }
    static async GetList() {
        return JC_GetList<PropertyDefectModel>(`${this.apiRoute}/getList`, {}, PropertyDefectModel);
    }
    static async Create(data: PropertyDefectModel) {
        return JC_Put<PropertyDefectModel>(this.apiRoute, data);
    }
    static async CreateList(dataList: PropertyDefectModel[]) {
        return JC_Put<PropertyDefectModel[]>(`${this.apiRoute}/createList`, dataList);
    }
    static async Update(data: PropertyDefectModel) {
        return JC_Post<PropertyDefectModel>(this.apiRoute, data);
    }
    static async UpdateList(dataList: PropertyDefectModel[]) {
        return JC_Post<PropertyDefectModel[]>(`${this.apiRoute}/updateList`, dataList);
    }
    static async Delete(id: string) {
        return JC_Delete(this.apiRoute, id);
    }
    static async DeleteList(ids: string[]) {
        return JC_Post(`${this.apiRoute}/deleteList`, { ids });
    }

    // - --------- - //
    // - VARIABLES - //
    // - --------- - //

    Id: string;
    PropertyId: string;
    DefectCategoryCode: string;
    Description: string;
    AreaCode: string;
    LocationCode: string;
    OrientationCode: string;
    Defects: string;
    ServerityCode: string;

    // - ----------- - //
    // - CONSTRUCTOR - //
    // - ----------- - //

    constructor(init?: Partial<PropertyDefectModel>) {
        super(init);
        this.Id = JC_Utils.generateGuid();
        this.PropertyId = "";
        this.DefectCategoryCode = "";
        this.Description = "";
        this.AreaCode = "";
        this.LocationCode = "";
        this.OrientationCode = "";
        this.Defects = "";
        this.ServerityCode = "";
        Object.assign(this, init);
    }

    // - ------ - //
    // - STRING - //
    // - ------ - //

    toString() {
        return `${this.Description} | ${this.Defects}`;
    }
}
